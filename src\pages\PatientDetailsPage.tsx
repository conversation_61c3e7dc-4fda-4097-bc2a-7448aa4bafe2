import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Calendar, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { usePatient } from '../contexts/PatientContext';

const PatientDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const {
    patientName, setPatientName,
    complaint, setComplaint,
    appointmentType, setAppointmentType,
    appointmentDate, setAppointmentDate,
    appointmentTime, setAppointmentTime,
    consents, setConsents
  } = usePatient();

  const [errors, setErrors] = useState({
    name: false,
    complaint: false,
    consents: false,
    appointmentTime: false
  });

  // Appointment scheduling states
  const [isAppointmentScheduled, setIsAppointmentScheduled] = useState(false);
  const [isAppointmentTimeReached, setIsAppointmentTimeReached] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState('');
  const [currentTime, setCurrentTime] = useState(new Date());

  // Timer effect to check appointment time
  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date();
      setCurrentTime(now);

      if (isAppointmentScheduled && appointmentDate && appointmentTime) {
        const appointmentDateTime = parseAppointmentDateTime(appointmentDate, appointmentTime);

        if (appointmentDateTime) {
          const timeDiff = appointmentDateTime.getTime() - now.getTime();

          if (timeDiff <= 0) {
            // Appointment time has reached
            setIsAppointmentTimeReached(true);
            setTimeRemaining('');
          } else {
            // Calculate remaining time
            const hours = Math.floor(timeDiff / (1000 * 60 * 60));
            const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

            setTimeRemaining(`${hours}h ${minutes}m ${seconds}s`);
            setIsAppointmentTimeReached(false);
          }
        }
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [isAppointmentScheduled, appointmentDate, appointmentTime]);

  // Parse appointment date and time
  const parseAppointmentDateTime = (date: string, time: string): Date | null => {
    try {
      // Assuming date format: "YYYY-MM-DD" or "DD/MM/YYYY" or similar
      // Assuming time format: "HH:MM" or "HH:MM AM/PM"

      let parsedDate: Date;

      // Try different date formats
      if (date.includes('/')) {
        // DD/MM/YYYY or MM/DD/YYYY format
        const [day, month, year] = date.split('/');
        parsedDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      } else if (date.includes('-')) {
        // YYYY-MM-DD format
        parsedDate = new Date(date);
      } else {
        return null;
      }

      // Parse time
      let hours = 0;
      let minutes = 0;

      if (time.toLowerCase().includes('am') || time.toLowerCase().includes('pm')) {
        // 12-hour format
        const isPM = time.toLowerCase().includes('pm');
        const timeOnly = time.replace(/am|pm/gi, '').trim();
        const [h, m] = timeOnly.split(':');
        hours = parseInt(h);
        minutes = parseInt(m) || 0;

        if (isPM && hours !== 12) hours += 12;
        if (!isPM && hours === 12) hours = 0;
      } else {
        // 24-hour format
        const [h, m] = time.split(':');
        hours = parseInt(h);
        minutes = parseInt(m) || 0;
      }

      parsedDate.setHours(hours, minutes, 0, 0);
      return parsedDate;
    } catch (error) {
      console.error('Error parsing appointment date/time:', error);
      return null;
    }
  };

  const handleConsent = (index: number) => {
    const newConsents = [...consents];
    newConsents[index] = !newConsents[index];
    setConsents(newConsents);
  };

  const handleProceed = () => {
    if (isAppointmentScheduled && !isAppointmentTimeReached) {
      // Appointment is scheduled but time hasn't reached yet
      return;
    }

    if (isAppointmentScheduled && isAppointmentTimeReached) {
      // Appointment time has reached, proceed to chat
      navigate('/chat');
      return;
    }

    // First time scheduling appointment
    const newErrors = {
      name: !patientName.trim(),
      complaint: !complaint.trim(),
      consents: !consents.some(consent => consent),
      appointmentTime: !appointmentDate.trim() || !appointmentTime.trim()
    };

    setErrors(newErrors);

    if (!newErrors.name && !newErrors.complaint && !newErrors.consents && !newErrors.appointmentTime) {
      // Validate appointment time is in the future
      const appointmentDateTime = parseAppointmentDateTime(appointmentDate, appointmentTime);

      if (!appointmentDateTime) {
        alert('Please enter a valid date and time format.\nDate: DD/MM/YYYY or YYYY-MM-DD\nTime: HH:MM or HH:MM AM/PM');
        return;
      }

      if (appointmentDateTime.getTime() <= new Date().getTime()) {
        alert('Please select a future date and time for your appointment.');
        return;
      }

      // Schedule the appointment
      setIsAppointmentScheduled(true);
      setIsAppointmentTimeReached(false);

      // Save all data (this will be handled by the context automatically)
      console.log('Appointment scheduled for:', appointmentDateTime);
    }
  };

  return (
    <div className="max-w-4xl w-full mx-auto">
      <div className="bg-white rounded-lg overflow-hidden shadow-lg">
        <div className="p-6">
          <h2 className="text-2xl font-bold text-center mb-2">Welcome to the healthcare POD</h2>
          <p className="text-gray-600 text-center mb-8 max-w-3xl mx-auto">
            Lorem ipsum dolor sit amet consectetur. Sagittis venenatis orci neque proin cras neque. Tellus id arcu mattis massa quam in. Aenean proin sed nisl duis tellus. Neque vulputate semper elit mattis pellentesque nisl at sed. Pellentesque suspendisse semper elit mattis pellentesque nisl at sed. Pellentesque suspendisse.
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Patient Details */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">Patient Details</h3>

              <div className="mb-4">
                <label htmlFor="name" className="block text-sm text-gray-600 mb-1">Name</label>
                <input
                  type="text"
                  id="name"
                  value={patientName}
                  onChange={(e) => setPatientName(e.target.value)}
                  disabled={isAppointmentScheduled}
                  className={`w-full p-2 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${isAppointmentScheduled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                />
                {errors.name && <p className="text-red-500 text-xs mt-1">Please enter your name</p>}
              </div>

              <div className="mb-4">
                <label htmlFor="complaint" className="block text-sm text-gray-600 mb-1">Complaint</label>
                <textarea
                  id="complaint"
                  value={complaint}
                  onChange={(e) => setComplaint(e.target.value)}
                  disabled={isAppointmentScheduled}
                  rows={6}
                  className={`w-full p-2 border ${errors.complaint ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 ${isAppointmentScheduled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                />
                {errors.complaint && <p className="text-red-500 text-xs mt-1">Please describe your complaint</p>}
              </div>
            </div>

            {/* Appointment Details */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">Appointment Details</h3>

              <div className="mb-4">
                <label className="block text-sm text-gray-600 mb-1">Appt. Type</label>
                <div className="flex items-center">
                  <div className="bg-indigo-100 p-2 rounded-md">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-700">
                      <rect width="18" height="18" x="3" y="3" rx="2" />
                      <path d="M3 9h18" />
                    </svg>
                  </div>
                  <select
                    value={appointmentType}
                    onChange={(e) => setAppointmentType(e.target.value as any)}
                    disabled={isAppointmentScheduled}
                    className={`ml-2 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 w-full ${isAppointmentScheduled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  >
                    <option value="Initial">Initial</option>
                    <option value="Follow-Up">Follow-Up</option>
                    <option value="Consultation">Consultation</option>
                  </select>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm text-gray-600 mb-1">Appt. Date</label>
                <div className="flex items-center">
                  <div className="bg-indigo-100 p-2 rounded-md">
                    <Calendar size={20} className="text-indigo-700" />
                  </div>
                  <input
                    type="text"
                    value={appointmentDate}
                    onChange={(e) => setAppointmentDate(e.target.value)}
                    disabled={isAppointmentScheduled}
                    placeholder="DD/MM/YYYY or YYYY-MM-DD"
                    className={`ml-2 p-2 border ${errors.appointmentTime ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 w-full ${isAppointmentScheduled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  />
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm text-gray-600 mb-1">Appt. Time</label>
                <div className="flex items-center">
                  <div className="bg-indigo-100 p-2 rounded-md">
                    <Clock size={20} className="text-indigo-700" />
                  </div>
                  <input
                    type="text"
                    value={appointmentTime}
                    onChange={(e) => setAppointmentTime(e.target.value)}
                    disabled={isAppointmentScheduled}
                    placeholder="HH:MM or HH:MM AM/PM"
                    className={`ml-2 p-2 border ${errors.appointmentTime ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 w-full ${isAppointmentScheduled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  />
                </div>
                {errors.appointmentTime && <p className="text-red-500 text-xs mt-1">Please enter appointment date and time</p>}
              </div>
            </div>
          </div>

          {/* Appointment Status Notification */}
          {isAppointmentScheduled && (
            <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                {isAppointmentTimeReached ? (
                  <CheckCircle className="w-6 h-6 text-green-600" />
                ) : (
                  <AlertCircle className="w-6 h-6 text-blue-600" />
                )}
                <div className="flex-1">
                  {isAppointmentTimeReached ? (
                    <div>
                      <h4 className="text-green-800 font-semibold">Appointment Time Reached!</h4>
                      <p className="text-green-700 text-sm">Your doctor is ready to see you. Click "Proceed to Chat" to start your consultation.</p>
                    </div>
                  ) : (
                    <div>
                      <h4 className="text-blue-800 font-semibold">Appointment Scheduled</h4>
                      <p className="text-blue-700 text-sm">
                        The doctor will reach you at your scheduled time: {appointmentDate} at {appointmentTime}
                      </p>
                      {timeRemaining && (
                        <p className="text-blue-600 text-sm font-medium mt-1">
                          Time remaining: {timeRemaining}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Consent Checkboxes */}
          <div className="mt-6 bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">Consent Agreements</h3>

            <div className={`mb-4 ${errors.consents ? 'text-red-500' : ''}`}>
              <label className="inline-flex items-start">
                <input
                  type="checkbox"
                  checked={consents[0]}
                  onChange={() => handleConsent(0)}
                  disabled={isAppointmentScheduled}
                  className={`form-checkbox h-5 w-5 text-indigo-600 mt-0.5 flex-shrink-0 ${isAppointmentScheduled ? 'cursor-not-allowed' : ''}`}
                />
                <span className="ml-3 text-sm leading-relaxed">
                  <strong>General Consent:</strong> I agree to receive medical information and guidance through this app and understand it is not a substitute for professional medical advice.
                </span>
              </label>
            </div>

            <div className="mb-4">
              <label className="inline-flex items-start">
                <input
                  type="checkbox"
                  checked={consents[1]}
                  onChange={() => handleConsent(1)}
                  disabled={isAppointmentScheduled}
                  className={`form-checkbox h-5 w-5 text-indigo-600 mt-0.5 flex-shrink-0 ${isAppointmentScheduled ? 'cursor-not-allowed' : ''}`}
                />
                <span className="ml-3 text-sm leading-relaxed">
                  <strong>Privacy Consent:</strong> I consent to the collection and use of my personal and health data in accordance with the app's privacy policy.
                </span>
              </label>
            </div>

            <div className="mb-4">
              <label className="inline-flex items-start">
                <input
                  type="checkbox"
                  checked={consents[2]}
                  onChange={() => handleConsent(2)}
                  disabled={isAppointmentScheduled}
                  className={`form-checkbox h-5 w-5 text-indigo-600 mt-0.5 flex-shrink-0 ${isAppointmentScheduled ? 'cursor-not-allowed' : ''}`}
                />
                <span className="ml-3 text-sm leading-relaxed">
                  <strong>Telemedicine Consent:</strong> I agree to consult with healthcare providers remotely via audio, video, or chat through this app.
                </span>
              </label>
            </div>

            {errors.consents && <p className="text-red-500 text-xs mt-2">Please consent to at least one item</p>}
          </div>

          <div className="mt-6 flex justify-end">
            <button
              onClick={handleProceed}
              disabled={isAppointmentScheduled && !isAppointmentTimeReached}
              className={`py-2 px-6 rounded-md font-medium transition-colors duration-300 ${
                isAppointmentScheduled && !isAppointmentTimeReached
                  ? 'bg-gray-400 cursor-not-allowed text-white'
                  : isAppointmentTimeReached
                  ? 'bg-green-600 hover:bg-green-700 text-white'
                  : 'bg-indigo-800 hover:bg-indigo-900 text-white'
              }`}
            >
              {isAppointmentScheduled && !isAppointmentTimeReached
                ? 'Waiting for Appointment Time'
                : isAppointmentTimeReached
                ? 'Proceed to Chat'
                : 'Schedule Appointment'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientDetailsPage;