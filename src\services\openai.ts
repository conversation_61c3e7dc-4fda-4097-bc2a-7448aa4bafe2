import OpenAI from 'openai';

const apiKey = "********************************************************************************************************************************************************************";

const openai = new OpenAI({
  apiKey,
  dangerouslyAllowBrowser: true // Note: In production, API calls should be made from a backend
});

export const getChatResponse = async (messages: Array<{ role: 'user' | 'assistant' | 'system', content: string }>) => {
  try {
    const completion = await openai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are Doctor <PERSON>, a knowledgeable and compassionate digital physician. Respond to patient inquiries with medical information, but always remind patients that you cannot provide official medical diagnoses and they should consult a real doctor for serious concerns. Keep responses concise and helpful.'
        },
        ...messages
      ],
      model: 'gpt-4o-mini',
    });

    return completion.choices[0].message.content;
  } catch (error) {
    console.error('Error calling OpenAI:', error);
    return "I'm sorry, I'm having trouble connecting to my medical database right now. Could you please try again in a moment?";
  }
};